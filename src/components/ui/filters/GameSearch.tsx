import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { Search, Database, Zap, Filter } from '@/lib/icons';
import { Game } from '@/types';
import { Button } from '@/components/ui/base/button';
import { Input } from '@/components/ui/base/input';
import { Card, CardContent } from '@/components/ui/base/card';
import { Badge } from '@/components/ui/base/badge';
import { SearchSuggestions } from './search-suggestions';
import { scoreAndSortGames, extractGamesFromScores, debugScoring } from '@/lib/utils/searchScoringUtils';
import { searchDebounce } from '@/lib/utils/smartDebounce';
import { processSearchError, createNoResultsError } from '@/lib/utils/searchErrorHandler';
import { deduplicateGames, getDeduplicationStats } from '@/lib/utils/gameDeduplicationUtils';

// Search options interface matching the API
interface SearchOptions {
  platforms?: string[];
  genres?: string[];
  minRating?: number;
  maxRating?: number;
  minYear?: number;
  maxYear?: number;
  sortBy?: 'relevance' | 'rating' | 'release_date' | 'name' | 'popularity';
  sortDirection?: 'asc' | 'desc';
  excludeDLC?: boolean;
  excludeExpansions?: boolean;
  searchType?: 'exact' | 'fuzzy' | 'smart';
  limit?: number;
}

interface GameSearchProps {
  onSearchResults: (games: Game[]) => void;
  onLoading: (loading: boolean) => void;
  onError: (error: string | null) => void;
  platformFilters?: string[]; // Add platform filters prop
}

export function GameSearch({ onSearchResults, onLoading, onError, platformFilters = [] }: GameSearchProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  
  const searchOptions = useMemo((): SearchOptions => ({
    platforms: platformFilters, // Use platform filters from props
    sortBy: 'relevance',
    sortDirection: 'desc',
    searchType: 'smart',
    excludeDLC: true,
    excludeExpansions: false,
    limit: platformFilters.length > 0 ? 100 : 80 // Increased limits for multi-API coverage
  }), [platformFilters]);
  
  const searchInputRef = useRef<HTMLInputElement>(null);


  const performSearch = useCallback(async (term: string, options: SearchOptions = {}) => {
    if (!term.trim()) {
      onError('Please enter a search term');
      return;
    }

    setIsSearching(true);
    onLoading(true);
    onError(null);
    setShowSuggestions(false);

    try {
      // Import the API clients dynamically
      const { igdbAPI, theGamesDBAPI, steamGridDBAPI } = await import('../../../lib/api/games');
      const { rawgAPI } = await import('../../../lib/api/rawg');

      console.log('🎮 Starting intelligent platform-aware search:', term, options);

      // Smart platform detection from search terms
      const platformKeywords = {
        'xbox': ['Xbox Series X/S', 'Xbox One', 'Xbox 360', 'Xbox'],
        'playstation': ['PlayStation 5', 'PlayStation 4', 'PlayStation 3', 'PlayStation 2', 'PlayStation'],
        'ps5': ['PlayStation 5'],
        'ps4': ['PlayStation 4'],
        'ps3': ['PlayStation 3'],
        'ps2': ['PlayStation 2'],
        'nintendo': ['Nintendo Switch', 'Nintendo 3DS', 'Nintendo DS', 'Wii U', 'Wii', 'GameCube', 'Nintendo 64'],
        'switch': ['Nintendo Switch'],
        'pc': ['PC'],
        'steam': ['PC'],
        'mobile': ['iOS', 'Android']
      };

      const searchLower = term.toLowerCase();
      let detectedPlatforms: string[] = [];
      
      // Detect platform keywords in search term
      for (const [keyword, platforms] of Object.entries(platformKeywords)) {
        if (searchLower.includes(keyword)) {
          detectedPlatforms = platforms;
          console.log(`🎯 Detected platform context: ${keyword} -> ${platforms.join(', ')}`);
          break;
        }
      }

      // Determine effective platform filters
      let effectivePlatforms: string[] = [];
      
      if (platformFilters.length > 0) {
        // Use platform filters from UI
        effectivePlatforms = platformFilters;
        console.log(`🎯 PLATFORM FILTER: Using UI platform filters: ${platformFilters.join(', ')}`);
        console.log(`🎯 PLATFORM FILTER: These filters will be sent to IGDB and TheGamesDB APIs`);
      } else if (detectedPlatforms.length > 0) {
        // Use detected platforms from search term
        effectivePlatforms = detectedPlatforms;
        console.log(`🎯 PLATFORM FILTER: Using detected platform context: ${detectedPlatforms.join(', ')}`);
        console.log(`🎯 PLATFORM FILTER: Auto-detected from search term, will be sent to APIs`);
      } else {
        console.log(`🔍 NO PLATFORM FILTER: Searching across all platforms`);
      }

      // Perform platform-aware searches
      let igdbPromise: Promise<unknown[]>;
      let tgdbPromise: Promise<unknown[]>;
      let sgdbPromise: Promise<unknown[]>;
      let rawgPromise: Promise<unknown[]>;

      if (effectivePlatforms.length > 0) {
        // Platform-filtered search
        const cleanSearchTerm = detectedPlatforms.length > 0 
          ? term.replace(/\b(xbox|playstation|ps[1-5]|nintendo|switch|pc|steam|mobile)\b/gi, '').trim()
          : term.trim();
        
        console.log(`🎮 Platform-specific search strategy for: ${effectivePlatforms.join(', ')}`);
        
        // Use platform-specific search strategies for better results
        if (effectivePlatforms.length === 1) {
          // Single platform search - use specialized method
          const platform = effectivePlatforms[0];
          console.log(`🎯 Using single platform search for: ${platform}`);
          
          igdbPromise = igdbAPI.searchByPlatform(platform, {
            ...searchOptions,
            ...options,
            limit: Math.min(options.limit || 40, 40) // Balanced limit for IGDB
          });

          // For TheGamesDB, use platform-specific search
          tgdbPromise = theGamesDBAPI.isConfigured()
            ? theGamesDBAPI.searchByPlatform(platform, cleanSearchTerm, {
                limit: Math.min(options.limit || 20, 20) // Moderate limit for TheGamesDB
              })
            : Promise.resolve([]);

          // SteamGridDB search for high-quality artwork and additional game data
          sgdbPromise = steamGridDBAPI.isConfigured()
            ? steamGridDBAPI.searchByPlatform(platform, cleanSearchTerm, {
                limit: Math.min(options.limit || 10, 10) // Lower limit for SteamGridDB (artwork focused)
              })
            : Promise.resolve([]);

          // RAWG search for comprehensive game database coverage
          rawgPromise = rawgAPI.isConfigured()
            ? rawgAPI.searchByPlatform(platform, cleanSearchTerm, {
                page_size: Math.min(options.limit || 30, 30) // Good balance for RAWG
              })
            : Promise.resolve([]);
        } else {
          // Multi-platform search - use general method with platform filters
          console.log(`🎯 Using multi-platform search for: ${effectivePlatforms.join(', ')}`);
          
          igdbPromise = igdbAPI.search(cleanSearchTerm, {
            ...searchOptions,
            ...options,
            platforms: effectivePlatforms,
            limit: Math.min(options.limit || 35, 35) // Balanced for multi-platform
          });

          tgdbPromise = theGamesDBAPI.isConfigured()
            ? theGamesDBAPI.searchByName(cleanSearchTerm, {
                platforms: effectivePlatforms,
                limit: Math.min(options.limit || 20, 20) // Moderate for multi-platform
              })
            : Promise.resolve([]);

          // SteamGridDB multi-platform search
          sgdbPromise = steamGridDBAPI.isConfigured()
            ? steamGridDBAPI.searchByName(cleanSearchTerm, {
                limit: Math.min(options.limit || 10, 10) // Lower for artwork
              })
            : Promise.resolve([]);

          // RAWG multi-platform search with platform filtering
          rawgPromise = rawgAPI.isConfigured()
            ? rawgAPI.searchByName(cleanSearchTerm, {
                page_size: Math.min(options.limit || 25, 25), // Balanced for multi-platform
                platforms: effectivePlatforms.map(p => {
                  // Map platform names to RAWG platform IDs if needed
                  const platformMap: Record<string, string> = {
                    'PC': '4',
                    'PlayStation 5': '187',
                    'PlayStation 4': '18',
                    'Xbox Series X/S': '186',
                    'Xbox One': '1',
                    'Nintendo Switch': '7'
                  };
                  return platformMap[p] || p;
                }).join(',')
              })
            : Promise.resolve([]);
        }
      } else {
        // General search across all platforms
        console.log(`🔍 Using general search across all platforms`);
        
        igdbPromise = igdbAPI.search(term.trim(), {
          ...searchOptions,
          ...options,
          limit: Math.min(options.limit || 30, 30) // Balanced for general search
        });

        tgdbPromise = theGamesDBAPI.isConfigured()
          ? theGamesDBAPI.searchByName(term.trim(), {
              limit: Math.min(options.limit || 20, 20) // Moderate for general search
            })
          : Promise.resolve([]);

        // SteamGridDB general search
        sgdbPromise = steamGridDBAPI.isConfigured()
          ? steamGridDBAPI.searchByName(term.trim(), {
              limit: Math.min(options.limit || 10, 10) // Lower for artwork focus
            })
          : Promise.resolve([]);

        // RAWG general search
        rawgPromise = rawgAPI.isConfigured()
          ? rawgAPI.searchByName(term.trim(), {
              page_size: Math.min(options.limit || 20, 20) // Balanced for general search
            })
          : Promise.resolve([]);
      }

      // Perform searches in parallel
      const [igdbGames, tgdbGames, sgdbGames, rawgGames] = await Promise.allSettled([igdbPromise, tgdbPromise, sgdbPromise, rawgPromise]);

      console.log(`✅ API searches completed`);
      console.log(`- IGDB: ${igdbGames.status === 'fulfilled' ? igdbGames.value.length : 0} games`);
      console.log(`- TheGamesDB: ${tgdbGames.status === 'fulfilled' ? tgdbGames.value.length : 0} games`);
      console.log(`- SteamGridDB: ${sgdbGames.status === 'fulfilled' ? sgdbGames.value.length : 0} games`);
      console.log(`- RAWG: ${rawgGames.status === 'fulfilled' ? rawgGames.value.length : 0} games`);
      
      // Debug: Log platform detection results
      if (detectedPlatforms.length > 0) {
        console.log(`🎯 Platform-specific search completed for: ${detectedPlatforms[0]}`);
      } else {
        console.log(`🔍 General search completed (no platform keywords detected)`);
      }

      const allGames: Game[] = [];

      // Process IGDB results
      if (igdbGames.status === 'fulfilled') {
        const convertedIGDB = igdbGames.value.map((igdbGame: unknown) => {
          const converted = igdbAPI.convertToGame(igdbGame);
          return {
            id: converted.id,
            title: converted.title,
            platforms: converted.platforms,
            genres: converted.genres,
            developer: converted.developer,
            publisher: converted.publisher,
            release_date: converted.release_date,
            description: converted.description,
            cover_image: converted.cover_image,
            screenshots: converted.screenshots,
            metacritic_score: converted.metacritic_score,
            youtube_links: converted.youtube_links,
            igdb_id: converted.igdb_id,
            api_source: 'igdb' as const,
            source_confidence: 0.95, // IGDB has high data quality
          } as Game;
        });

        allGames.push(...convertedIGDB);
      }

      // Process TheGamesDB results
      if (tgdbGames.status === 'fulfilled' && tgdbGames.value.length > 0) {
        // Get images for TheGamesDB games
        const gameIds = tgdbGames.value.map((g: { id: string }) => g.id);
        const imageMap = await theGamesDBAPI.getGameImages(gameIds, ['boxart', 'screenshot']);

        const convertedTGDB = tgdbGames.value.map((tgdbGame: unknown) => {
          const gameImages = imageMap[tgdbGame.id] || [];
          const converted = theGamesDBAPI.convertToGame(tgdbGame, gameImages);
          return {
            id: converted.id,
            title: converted.title,
            platforms: converted.platforms,
            genres: converted.genres,
            developer: converted.developer,
            publisher: converted.publisher,
            release_date: converted.release_date,
            description: converted.description,
            cover_image: converted.cover_image,
            screenshots: converted.screenshots,
            metacritic_score: converted.metacritic_score,
            youtube_links: converted.youtube_links,
            igdb_id: converted.igdb_id,
            api_source: 'tgdb' as const,
            source_confidence: 0.80, // TheGamesDB has good data quality
          } as Game;
        });

        allGames.push(...convertedTGDB);
      }

      // Process SteamGridDB results
      if (sgdbGames.status === 'fulfilled' && sgdbGames.value.length > 0) {
        const convertedSGDB = sgdbGames.value.map((sgdbGame: unknown) => {
          const converted = steamGridDBAPI.convertToGame(sgdbGame);
          return {
            id: converted.id,
            title: converted.title,
            platforms: converted.platforms,
            genres: converted.genres,
            developer: converted.developer,
            publisher: converted.publisher,
            release_date: converted.release_date,
            description: converted.description,
            cover_image: converted.cover_image,
            screenshots: converted.screenshots,
            metacritic_score: converted.metacritic_score,
            youtube_links: converted.youtube_links,
            igdb_id: converted.igdb_id,
            api_source: 'sgdb' as const,
            source_confidence: 0.70, // SteamGridDB focused on artwork
          } as Game;
        });

        allGames.push(...convertedSGDB);
      }

      // Process RAWG results
      if (rawgGames.status === 'fulfilled' && rawgGames.value.length > 0) {
        const convertedRAWG = rawgGames.value.map((rawgGame: unknown) => {
          const converted = rawgAPI.convertToGame(rawgGame);
          return {
            id: converted.id,
            title: converted.title,
            platforms: converted.platforms,
            genres: converted.genres,
            developer: converted.developer,
            publisher: converted.publisher,
            release_date: converted.release_date,
            description: converted.description,
            cover_image: converted.cover_image,
            screenshots: converted.screenshots,
            metacritic_score: converted.metacritic_score,
            youtube_links: converted.youtube_links,
            igdb_id: converted.igdb_id,
            api_source: 'rawg' as const,
            source_confidence: 0.90, // RAWG has excellent data quality
          } as Game;
        });

        allGames.push(...convertedRAWG);
      }

      // Advanced deduplication with intelligent merging
      const deduplicatedGames = deduplicateGames(allGames, {
        titleSimilarityThreshold: 0.75,
        platformSpecific: effectivePlatforms.length > 0, // Be stricter if platform filtering
        mergeStrategy: 'prefer_igdb',
        allowPlatformVariants: effectivePlatforms.length === 0 // Allow variants for general searches
      });

      // Log deduplication stats
      const stats = getDeduplicationStats(allGames, deduplicatedGames);
      console.log(`📊 Deduplication stats:`, stats);

      // Apply intelligent scoring and sorting to deduplicated games
      const scoredGames = scoreAndSortGames(deduplicatedGames, term, {
        searchedPlatforms: effectivePlatforms,
        preferredPlatforms: platformFilters,
        minScore: 0.1,
        weights: {
          titleSimilarity: 0.6,   // Boost title matching for search results
          platformRelevance: 0.2,
          recencyScore: 0.1,
          ratingScore: 0.1
        }
      });

      // Extract sorted games from scores
      const sortedResults = extractGamesFromScores(scoredGames);

      // Debug scoring in development
      if (process.env.NODE_ENV === 'development') {
        debugScoring(scoredGames.slice(0, 10), term);
      }

      console.log(`📊 Final results: ${sortedResults.length} games (from ${allGames.length} raw → ${deduplicatedGames.length} deduplicated)`);
      
      if (sortedResults.length > 0) {
        console.log(`🎯 Top result: "${sortedResults[0]?.title}" (Score: ${scoredGames[0]?.breakdown.totalScore})`);
        onSearchResults(sortedResults);
        onError(null); // Clear any previous errors
      } else {
        // Handle no results case with helpful suggestions
        const noResultsError = createNoResultsError(term);
        console.log(`❌ No results found for "${term}" - providing suggestions`);
        onError(noResultsError.userMessage + '\n\nSuggestions:\n' + noResultsError.suggestions.map(s => `• ${s}`).join('\n'));
        onSearchResults([]);
      }
    } catch (error) {
      console.error('❌ Search error:', error);
      
      // Process error with enhanced error handler
      const processedError = processSearchError(error, term);
      
      // Create user-friendly error message
      const userErrorMessage = processedError.userMessage + 
        (processedError.suggestions.length > 0 
          ? '\n\nSuggestions:\n' + processedError.suggestions.map(s => `• ${s}`).join('\n')
          : '');
      
      // Log detailed error info for debugging
      console.error('📊 Processed error details:', {
        type: processedError.type,
        retryable: processedError.retryable,
        query: term,
        suggestions: processedError.suggestions
      });
      
      onError(userErrorMessage);
      onSearchResults([]);
    } finally {
      onLoading(false);
      setIsSearching(false);
    }
  }, [onLoading, onError, onSearchResults, platformFilters, searchOptions]);

  // Smart debounced search for real-time results
  const debouncedSearch = useMemo(() => 
    searchDebounce((term: string) => {
      if (term.trim().length >= 2) {
        performSearch(term);
      }
    }, {
      delay: 300,           // Base delay reduced from 800ms
      minDelay: 150,        // Minimum delay for similar queries
      maxDelay: 500,        // Maximum delay for different queries
      similarityThreshold: 0.6,  // Lower threshold for more aggressive optimization
      adaptiveScaling: true
    })
  , [performSearch]);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    await performSearch(searchTerm);
  };

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    
    // Show suggestions when typing
    if (value.length > 0) {
      setShowSuggestions(true);
    }
    
    // Trigger debounced search for real-time results
    if (value.length >= 2) {
      debouncedSearch(value);
    }
  };

  const handleSuggestionSelect = (suggestion: string) => {
    setSearchTerm(suggestion);
    performSearch(suggestion);
  };

  const handleInputFocus = () => {
    setShowSuggestions(true);
  };

  const handleInputBlur = () => {
    // Delay hiding suggestions to allow clicks
    setTimeout(() => setShowSuggestions(false), 200);
  };


  // Cleanup debounce function on unmount
  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  return (
    <div className="w-full max-w-4xl mx-auto p-6 animate-stagger-fade">
      <div className="text-center mb-12">
        <div className="flex items-center justify-center gap-3 mb-4 group">
          <div className="p-3 bg-primary/10 rounded-2xl group-hover:bg-primary/20 transition-all duration-300 group-hover:scale-110">
            <Search className="h-8 w-8 text-primary group-hover:animate-float" />
          </div>
          <h1 className="text-5xl font-bold bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent animate-glow">
            Game Discovery
          </h1>
        </div>
        <p className="text-muted-foreground text-lg mb-6">
          Discover games across all platforms powered by IGDB, RAWG, TheGamesDB & SteamGridDB
        </p>
        <div className="flex justify-center gap-3 flex-wrap">
          <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20 hover:scale-105 transition-all duration-200 hover:shadow-lg hover:shadow-primary/20">
            <Database className="h-3 w-3 mr-1 animate-glow" />
            IGDB Database
          </Badge>
          <Badge variant="secondary" className="bg-secondary/10 text-secondary border-secondary/20 hover:scale-105 transition-all duration-200 hover:shadow-lg hover:shadow-secondary/20">
            <Database className="h-3 w-3 mr-1 animate-glow" />
            TheGamesDB
          </Badge>
          <Badge variant="secondary" className="bg-green-500/10 text-green-500 border-green-500/20 hover:scale-105 transition-all duration-200 hover:shadow-lg hover:shadow-green-500/20">
            <Database className="h-3 w-3 mr-1 animate-glow" />
            SteamGridDB
          </Badge>
          <Badge variant="secondary" className="bg-accent/10 text-accent border-accent/20 hover:scale-105 transition-all duration-200 hover:shadow-lg hover:shadow-accent/20">
            <Zap className="h-3 w-3 mr-1 animate-glow" />
            Triple API Search
          </Badge>
        </div>
      </div>

      <Card className="border-2 border-primary/20 shadow-2xl shadow-primary/5 hover:shadow-primary/10 transition-all duration-300">
        <CardContent className="p-6 space-y-6 overflow-visible">
          {/* Main Search Input */}
          <form onSubmit={handleSearch} className="flex gap-4">
            <div className="relative flex-1 group overflow-visible">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground group-focus-within:text-primary transition-colors duration-200 group-focus-within:scale-110" />
              <Input
                ref={searchInputRef}
                type="text"
                value={searchTerm}
                onChange={handleSearchInputChange}
                onFocus={handleInputFocus}
                onBlur={handleInputBlur}
                placeholder="Search for any game title... (e.g., 'Cyberpunk', 'Zelda', 'Call of Duty')"
                className="pl-10 h-12 text-lg border-primary/20 focus:border-primary focus:shadow-lg focus:shadow-primary/10 transition-all duration-200"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleSearch(e as unknown as React.FormEvent);
                  }
                  if (e.key === 'Escape') {
                    setShowSuggestions(false);
                  }
                }}
              />
              <SearchSuggestions
                searchTerm={searchTerm}
                onSuggestionSelect={handleSuggestionSelect}
                isVisible={showSuggestions}
                onClose={() => setShowSuggestions(false)}
              />
            </div>
            <Button
              type="submit"
              size="lg"
              disabled={isSearching}
              className="h-12 px-8 bg-gradient-to-r from-primary via-primary to-secondary hover:from-primary/90 hover:via-primary/90 hover:to-secondary/90 hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl hover:shadow-primary/20"
            >
              <Search className={`h-5 w-5 mr-2 ${isSearching ? 'animate-spin' : ''}`} />
              {isSearching ? 'Searching...' : 'Search Games'}
            </Button>
          </form>

          {/* Smart Search Info */}
          <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
            <Filter className="h-4 w-4" />
            <span>Smart search with fuzzy matching enabled</span>
          </div>

          {/* Search Tips */}
          <div className="text-center">
            <p className="text-sm text-muted-foreground">
              💡 <strong>Pro Tips:</strong> Use quotes for exact matches ("Call of Duty"), 
              try different spellings, or search by genre + platform combinations
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}