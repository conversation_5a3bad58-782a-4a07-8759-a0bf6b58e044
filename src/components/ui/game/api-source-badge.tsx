import { Badge } from '@/components/ui/base/badge';
import { Database, Gamepad2, Image, Star } from 'lucide-react';
import { cn } from '@/lib/utils';

interface APISourceBadgeProps {
  source: 'igdb' | 'rawg' | 'tgdb' | 'sgdb' | 'merged';
  confidence?: number;
  className?: string;
  showIcon?: boolean;
  showConfidence?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

const sourceConfig = {
  igdb: {
    label: 'IGDB',
    icon: Database,
    color: 'bg-purple-600/90 text-white border-purple-500/50 hover:bg-purple-500/90',
    description: 'Internet Game Database'
  },
  rawg: {
    label: 'RAWG',
    icon: Gamepad2,
    color: 'bg-orange-600/90 text-white border-orange-500/50 hover:bg-orange-500/90',
    description: 'RAWG Video Games Database'
  },
  tgdb: {
    label: 'TGDB',
    icon: Database,
    color: 'bg-blue-600/90 text-white border-blue-500/50 hover:bg-blue-500/90',
    description: 'TheGamesDB'
  },
  sgdb: {
    label: 'SGDB',
    icon: Image,
    color: 'bg-green-600/90 text-white border-green-500/50 hover:bg-green-500/90',
    description: 'SteamGridDB'
  },
  merged: {
    label: 'Multi',
    icon: Star,
    color: 'bg-gradient-to-r from-purple-600/90 to-orange-600/90 text-white border-purple-500/50 hover:from-purple-500/90 hover:to-orange-500/90',
    description: 'Merged from multiple sources'
  }
};

const sizeConfig = {
  sm: {
    badge: 'text-xs px-1.5 py-0.5 h-5',
    icon: 'h-2.5 w-2.5',
    gap: 'gap-1'
  },
  md: {
    badge: 'text-xs px-2 py-1 h-6',
    icon: 'h-3 w-3',
    gap: 'gap-1.5'
  },
  lg: {
    badge: 'text-sm px-3 py-1.5 h-7',
    icon: 'h-4 w-4',
    gap: 'gap-2'
  }
};

export function APISourceBadge({ 
  source, 
  confidence, 
  className, 
  showIcon = true, 
  showConfidence = false,
  size = 'sm'
}: APISourceBadgeProps) {
  const config = sourceConfig[source];
  const sizeStyles = sizeConfig[size];
  const Icon = config.icon;

  const confidenceText = confidence ? ` (${Math.round(confidence * 100)}%)` : '';

  return (
    <Badge
      variant="secondary"
      className={cn(
        'font-medium transition-all duration-200 backdrop-blur-sm',
        config.color,
        sizeStyles.badge,
        showIcon && sizeStyles.gap,
        'flex items-center',
        className
      )}
      title={`${config.description}${confidenceText}`}
    >
      {showIcon && <Icon className={sizeStyles.icon} />}
      <span>{config.label}</span>
      {showConfidence && confidence && (
        <span className="opacity-80">
          {Math.round(confidence * 100)}%
        </span>
      )}
    </Badge>
  );
}

// Multi-source badge for showing all contributing APIs
interface MultiSourceBadgeProps {
  sources: Array<{
    source: 'igdb' | 'rawg' | 'tgdb' | 'sgdb';
    confidence?: number;
  }>;
  className?: string;
  maxVisible?: number;
}

export function MultiSourceBadge({ 
  sources, 
  className, 
  maxVisible = 3 
}: MultiSourceBadgeProps) {
  const visibleSources = sources.slice(0, maxVisible);
  const hiddenCount = sources.length - maxVisible;

  return (
    <div className={cn('flex items-center gap-1', className)}>
      {visibleSources.map((item, index) => (
        <APISourceBadge
          key={`${item.source}-${index}`}
          source={item.source}
          confidence={item.confidence}
          size="sm"
          showIcon={false}
        />
      ))}
      {hiddenCount > 0 && (
        <Badge
          variant="secondary"
          className="text-xs px-1.5 py-0.5 h-5 bg-gray-600/90 text-white border-gray-500/50"
        >
          +{hiddenCount}
        </Badge>
      )}
    </div>
  );
}

// API statistics component for search results summary
interface APIStatsProps {
  stats: Record<string, number>;
  total: number;
  className?: string;
}

export function APIStats({ stats, total, className }: APIStatsProps) {
  const sortedStats = Object.entries(stats)
    .filter(([, count]) => count > 0)
    .sort(([, a], [, b]) => b - a);

  return (
    <div className={cn('flex flex-wrap items-center gap-2', className)}>
      <span className="text-sm text-muted-foreground">Sources:</span>
      {sortedStats.map(([source, count]) => {
        const percentage = Math.round((count / total) * 100);
        return (
          <div key={source} className="flex items-center gap-1">
            <APISourceBadge 
              source={source as any} 
              size="sm" 
              showIcon={true}
            />
            <span className="text-xs text-muted-foreground">
              {count} ({percentage}%)
            </span>
          </div>
        );
      })}
    </div>
  );
}
