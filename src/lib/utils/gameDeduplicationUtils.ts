import { Game } from '@/types';

/**
 * Advanced game deduplication with smart merging
 * Handles variations in titles, platform differences, and data merging
 */

interface GameMatch {
  game1: Game;
  game2: Game;
  similarity: number;
  reasons: string[];
}

interface DeduplicationOptions {
  titleSimilarityThreshold: number;
  platformSpecific: boolean;
  mergeStrategy: 'preserve_first' | 'merge_best' | 'prefer_igdb' | 'prefer_rawg' | 'smart_merge';
  allowPlatformVariants: boolean;
}

// Refined deduplication settings (as per search-improvements.md)
const DEFAULT_OPTIONS: DeduplicationOptions = {
  titleSimilarityThreshold: 0.7,     // Lowered from 0.8 to be less aggressive
  platformSpecific: false,           // Changed from true to preserve more variants
  mergeStrategy: 'smart_merge',       // Use smart merging for multi-API results
  allowPlatformVariants: true        // Changed from false to allow platform variants
};

/**
 * Normalize title for comparison with enhanced RAWG support
 */
function normalizeTitle(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ')     // Replace special chars with spaces
    .replace(/\s+/g, ' ')         // Collapse multiple spaces
    .replace(/\b(the|a|an)\b/g, '') // Remove articles
    .replace(/\b(edition|remastered|definitive|enhanced|ultimate|deluxe|goty|game of the year)\b/g, '') // Remove edition suffixes
    .replace(/\b(hd|4k|vr|pc|ps\d|xbox|switch)\b/g, '') // Remove platform/quality indicators
    .trim();
}

/**
 * Calculate Jaccard similarity between two strings
 */
function jaccardSimilarity(str1: string, str2: string): number {
  const set1 = new Set(str1.split(' ').filter(w => w.length > 0));
  const set2 = new Set(str2.split(' ').filter(w => w.length > 0));
  
  const set1Array = Array.from(set1);
  const intersection = new Set(set1Array.filter(x => set2.has(x)));
  const union = new Set([...set1Array, ...Array.from(set2)]);
  
  return union.size === 0 ? 0 : intersection.size / union.size;
}

/**
 * Advanced title similarity calculation
 */
function calculateTitleSimilarity(title1: string, title2: string): number {
  const norm1 = normalizeTitle(title1);
  const norm2 = normalizeTitle(title2);
  
  // Exact match
  if (norm1 === norm2) return 1.0;
  
  // Jaccard similarity
  const jaccardScore = jaccardSimilarity(norm1, norm2);
  
  // Substring matching
  let substringScore = 0;
  if (norm1.includes(norm2) || norm2.includes(norm1)) {
    const shorter = norm1.length < norm2.length ? norm1 : norm2;
    const longer = norm1.length >= norm2.length ? norm1 : norm2;
    substringScore = shorter.length / longer.length;
  }
  
  // Combined score
  return Math.max(jaccardScore, substringScore * 0.8);
}

/**
 * Check platform compatibility
 */
function arePlatformsCompatible(platforms1: string[], platforms2: string[], platformSpecific: boolean): boolean {
  if (!platformSpecific) return true;
  if (!platforms1?.length || !platforms2?.length) return true;
  
  // Check for any platform overlap
  const set1 = new Set(platforms1.map(p => p.toLowerCase()));
  const set2 = new Set(platforms2.map(p => p.toLowerCase()));
  
  return Array.from(set1).some(p => set2.has(p));
}

/**
 * Check if release dates are similar (within 2 years)
 */
function areReleaseDatesCompatible(date1?: string, date2?: string): boolean {
  if (!date1 || !date2) return true;
  
  try {
    const d1 = new Date(date1);
    const d2 = new Date(date2);
    const yearDiff = Math.abs(d1.getFullYear() - d2.getFullYear());
    return yearDiff <= 2;
  } catch {
    return true;
  }
}

/**
 * Find if two games are potential matches
 */
function findGameMatch(game1: Game, game2: Game, options: DeduplicationOptions): GameMatch | null {
  const titleSim = calculateTitleSimilarity(game1.title, game2.title);
  
  // Basic title similarity check
  if (titleSim < options.titleSimilarityThreshold) {
    return null;
  }
  
  const reasons: string[] = [];
  let totalSimilarity = titleSim;
  
  reasons.push(`Title similarity: ${Math.round(titleSim * 100)}%`);
  
  // Platform compatibility
  const platformsCompatible = arePlatformsCompatible(
    game1.platforms || [], 
    game2.platforms || [], 
    options.platformSpecific
  );
  
  if (!platformsCompatible && !options.allowPlatformVariants) {
    return null;
  }
  
  if (platformsCompatible) {
    reasons.push('Compatible platforms');
    totalSimilarity += 0.1;
  }
  
  // Release date compatibility
  if (areReleaseDatesCompatible(game1.release_date, game2.release_date)) {
    reasons.push('Similar release dates');
    totalSimilarity += 0.1;
  }
  
  // Developer/Publisher similarity
  if (game1.developer && game2.developer) {
    const devSim = calculateTitleSimilarity(game1.developer, game2.developer);
    if (devSim > 0.7) {
      reasons.push('Same developer');
      totalSimilarity += 0.1;
    }
  }
  
  return {
    game1,
    game2,
    similarity: Math.min(totalSimilarity, 1.0),
    reasons
  };
}

/**
 * Get API source priority for smart merging
 */
function getAPISourcePriority(gameId: string): number {
  if (gameId.startsWith('igdb_')) return 4; // Highest priority - most comprehensive data
  if (gameId.startsWith('rawg_')) return 3; // High priority - good metadata and ratings
  if (gameId.startsWith('tgdb_')) return 2; // Medium priority - good for artwork
  if (gameId.startsWith('sgdb_')) return 1; // Lower priority - mainly artwork
  return 0; // Unknown source
}

/**
 * Merge two games intelligently
 */
function mergeGames(game1: Game, game2: Game, strategy: DeduplicationOptions['mergeStrategy']): Game {
  if (strategy === 'preserve_first') {
    return game1;
  }

  if (strategy === 'prefer_igdb') {
    const igdbGame = game1.id.startsWith('igdb_') ? game1 : game2;
    const otherGame = igdbGame === game1 ? game2 : game1;

    return {
      ...igdbGame,
      // Merge better images from other source
      cover_image: igdbGame.cover_image || otherGame.cover_image,
      screenshots: [...(igdbGame.screenshots || []), ...(otherGame.screenshots || [])].slice(0, 10),
      // Merge video links
      youtube_links: [...(igdbGame.youtube_links || []), ...(otherGame.youtube_links || [])].slice(0, 5),
    };
  }

  if (strategy === 'prefer_rawg') {
    const rawgGame = game1.id.startsWith('rawg_') ? game1 : game2;
    const otherGame = rawgGame === game1 ? game2 : game1;

    return {
      ...rawgGame,
      // Merge better images from other source
      cover_image: rawgGame.cover_image || otherGame.cover_image,
      screenshots: [...(rawgGame.screenshots || []), ...(otherGame.screenshots || [])].slice(0, 10),
      // Merge video links
      youtube_links: [...(rawgGame.youtube_links || []), ...(otherGame.youtube_links || [])].slice(0, 5),
      // Keep IGDB ID if available from other source
      igdb_id: rawgGame.igdb_id || otherGame.igdb_id,
    };
  }

  if (strategy === 'smart_merge') {
    // Choose primary game based on API source priority
    const priority1 = getAPISourcePriority(game1.id);
    const priority2 = getAPISourcePriority(game2.id);
    const primaryGame = priority1 >= priority2 ? game1 : game2;
    const secondaryGame = primaryGame === game1 ? game2 : game1;

    return {
      ...primaryGame,
      // Smart field merging - choose best available data
      platforms: Array.from(new Set([...(primaryGame.platforms || []), ...(secondaryGame.platforms || [])])),
      genres: Array.from(new Set([...(primaryGame.genres || []), ...(secondaryGame.genres || [])])),
      developer: primaryGame.developer || secondaryGame.developer,
      publisher: primaryGame.publisher || secondaryGame.publisher,
      release_date: primaryGame.release_date || secondaryGame.release_date,
      description: (primaryGame.description && primaryGame.description.length > (secondaryGame.description?.length || 0))
        ? primaryGame.description
        : secondaryGame.description || primaryGame.description,
      cover_image: primaryGame.cover_image || secondaryGame.cover_image,
      screenshots: Array.from(new Set([...(primaryGame.screenshots || []), ...(secondaryGame.screenshots || [])])).slice(0, 10),
      metacritic_score: primaryGame.metacritic_score || secondaryGame.metacritic_score,
      youtube_links: Array.from(new Set([...(primaryGame.youtube_links || []), ...(secondaryGame.youtube_links || [])])).slice(0, 5),
      igdb_id: primaryGame.igdb_id || secondaryGame.igdb_id,
      api_source: 'merged' as const,
      source_confidence: Math.max(primaryGame.source_confidence || 0, secondaryGame.source_confidence || 0),
    };
  }
  
  // merge_best strategy
  const merged: Game = {
    id: game1.id, // Keep first ID
    title: game1.title, // Keep first title
    platforms: Array.from(new Set([...((game1.platforms || [])), ...((game2.platforms || []))])),
    genres: Array.from(new Set([...((game1.genres || [])), ...((game2.genres || []))])),
    developer: game1.developer || game2.developer,
    publisher: game1.publisher || game2.publisher,
    release_date: game1.release_date || game2.release_date,
    description: (game1.description && game1.description.length > (game2.description?.length || 0)) 
      ? game1.description 
      : game2.description || game1.description,
    cover_image: game1.cover_image || game2.cover_image,
    screenshots: Array.from(new Set([...((game1.screenshots || [])), ...((game2.screenshots || []))])).slice(0, 10),
    metacritic_score: game1.metacritic_score || game2.metacritic_score,
    youtube_links: Array.from(new Set([...((game1.youtube_links || [])), ...((game2.youtube_links || []))])).slice(0, 5),
    igdb_id: game1.igdb_id || game2.igdb_id,
  };
  
  return merged;
}

/**
 * Deduplicate an array of games
 */
export function deduplicateGames(
  games: Game[], 
  options: Partial<DeduplicationOptions> = {}
): Game[] {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  const result: Game[] = [];
  const processed = new Set<string>();
  
  console.log(`🔍 Starting deduplication of ${games.length} games...`);
  
  for (let i = 0; i < games.length; i++) {
    const game1 = games[i];
    
    if (processed.has(game1.id)) {
      continue;
    }
    
    let bestMerged = game1;
    const duplicateIds = new Set([game1.id]);
    
    // Look for matches in remaining games
    for (let j = i + 1; j < games.length; j++) {
      const game2 = games[j];
      
      if (processed.has(game2.id)) {
        continue;
      }
      
      const match = findGameMatch(game1, game2, opts);
      
      if (match && match.similarity >= opts.titleSimilarityThreshold) {
        console.log(`🎯 Found duplicate: "${game1.title}" ↔ "${game2.title}" (${Math.round(match.similarity * 100)}%)`);
        console.log(`   Reasons: ${match.reasons.join(', ')}`);
        
        // Merge the games
        bestMerged = mergeGames(bestMerged, game2, opts.mergeStrategy);
        duplicateIds.add(game2.id);
        processed.add(game2.id);
      }
    }
    
    result.push(bestMerged);
    processed.add(game1.id);
    
    if (duplicateIds.size > 1) {
      console.log(`✅ Merged ${duplicateIds.size} duplicates into: "${bestMerged.title}"`);
    }
  }
  
  console.log(`📊 Deduplication complete: ${games.length} → ${result.length} games (${games.length - result.length} duplicates removed)`);
  
  return result;
}

/**
 * Find all potential duplicates without merging (for debugging)
 */
export function findDuplicates(
  games: Game[], 
  options: Partial<DeduplicationOptions> = {}
): GameMatch[] {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  const matches: GameMatch[] = [];
  
  for (let i = 0; i < games.length; i++) {
    for (let j = i + 1; j < games.length; j++) {
      const match = findGameMatch(games[i], games[j], opts);
      if (match && match.similarity >= opts.titleSimilarityThreshold) {
        matches.push(match);
      }
    }
  }
  
  return matches.sort((a, b) => b.similarity - a.similarity);
}

/**
 * Get deduplication statistics
 */
export function getDeduplicationStats(original: Game[], deduplicated: Game[]): object {
  return {
    originalCount: original.length,
    deduplicatedCount: deduplicated.length,
    duplicatesRemoved: original.length - deduplicated.length,
    reductionPercentage: Math.round(((original.length - deduplicated.length) / original.length) * 100),
    avgGamesPerPlatform: deduplicated.reduce((acc, game) => acc + (game.platforms?.length || 1), 0) / deduplicated.length
  };
}